use bumpalo::Bump;
use color_eyre::{eyre::WrapErr, Result};
use parquet_provider::hdfs_provider::HdfsConfig;
use std::time::Instant;

use crate::config::DwTestItemConfig;
use crate::service::ads::FtYmsAdsTestItemService;
use crate::service::ft_dws_test_item_service::FtDwsTestItemService;
use crate::task::ft_task_params::FtTaskParams;
use common::ck::ck_operate::CkOperate;
use common::ck::ck_sink::SinkHandler;
use common::dto::dwd::die_detail_parquet::DieDetailParquet;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dto::ods::test_item_data_parquet::TestItemDataParquet;
use common::dwd::sink::test_item_detail_handler::TestItemDetail<PERSON><PERSON><PERSON>;

use crate::service::dwd::ft_dwd_test_item_service::FtDwdTestItemService;
use common::dwd::util::dwd_common_util::DwdCommonUtil;
use common::model::constant;
use common::model::constant::run_mode::RunMode;
use common::model::key::wafer_key::WaferKey;
use common::utils::path;
use parquet_provider::parquet_provider::read_parquet;
use parquet_provider::parquet_provider::read_parquet_multi;
use crate::cpu_limit::init_cpu_limit;

// Corresponding to Scala file:
// /dataware/dataware-dw/dataware-dw-test-item/src/main/scala/com/guwave/onedata/dataware/dw/testItem/spark/task/impl/FtTestItemTask.scala

/// FtTestItemTask orchestrates the complete FT test item data processing pipeline
/// This is the main entry point for FT stage data warehouse processing
#[derive(Debug)]
pub struct FtTestItemTask {
    config: DwTestItemConfig,
}

impl FtTestItemTask {
    /// Create new FtTestItemTask with configuration
    pub fn new() -> Self {
        let config = DwTestItemConfig::get_config().unwrap();
        Self { config }
    }

    /// Execute the complete FT test item task
    ///
    /// Corresponds to: FtTestItemTask.scala doTask method
    /// private def doTask(customer: String, subCustomer: String, factory: String, factorySite: String,
    ///                   testArea: String, lotId: String, waferNo: String, lotType: String, deviceId: String,
    ///                   testStage: String, mode: String, fileCategory: String, ckSinkType: String,
    ///                   testItemDetailResultPartition: String, dataVersion: String, calculateYmsTestItem: String): Unit
    pub async fn do_task(&self, params: FtTaskParams) -> Result<()> {
        // 首先设置CPU限制
        init_cpu_limit();

        let start_time = Instant::now();
        log::info!("ft test item task start...");
        let bump = Bump::new();

        self.execute_tombstone_operations(
            &params.customer,
            &params.factory,
            &params.test_area,
            &params.lot_id,
            &params.lot_type,
            &params.test_stage,
            &params.device_id,
            &params.wafer_no,
        )
        .await
        .wrap_err("执行tombstone操作失败")?;

        // FT使用die_detail_result_dir而不是cp_die_detail_result_dir，并且使用lot路径而不是wafer路径
        let die_detail_path = path::get_dwd_lot_path(
            &self.config.die_detail_result_dir,
            &params.test_area,
            &params.customer,
            &params.factory,
            &params.lot_id,
            &params.device_id,
            &params.test_stage,
            &params.lot_type,
        );

        // FT使用ft_result_dir而不是cp_result_dir
        let base_path = if self.config.ft_result_dir.ends_with(constant::SLASH) {
            self.config.ft_result_dir.clone()
        } else {
            format!("{}{}", self.config.ft_result_dir, constant::SLASH)
        };

        let test_item_data_path = path::get_ods_lot_path(
            &base_path,
            &params.file_category,
            &params.test_area,
            &params.customer,
            &params.factory,
            constant::TEST_ITEM_DATA,
            &params.lot_id,
            &params.device_id,
            &params.test_stage,
            &params.lot_type,
        );

        log::info!("当前Task需要计算的数据集: [DieDetail={}], [TestItemData={}]", die_detail_path, test_item_data_path);
        let die_detail = self.load_die_detail_data(&die_detail_path).await?;
        let test_item_data: Vec<Vec<TestItemDataParquet>> = self.load_test_item_data(&test_item_data_path).await?;

        // 计算dwd test item
        let wafer_key = self.create_wafer_key(&params);
        let dwd_calculate_result = bump.alloc(
            FtDwdTestItemService::new(self.config.test_item_detail_result_partition, params.test_area.clone())
                .calculate(
                    die_detail,
                    test_item_data,
                    &wafer_key,
                    params.test_area.clone(),
                    params.execute_mode.clone(),
                    params.file_category.clone(),
                    params.ck_sink_type.clone(),
                    RunMode::RUST.to_string(),
                    &self.config,
                )
                .await
                .map_err(|e| {
                    let err_str = e.to_string();
                    log::error!("计算dwd test item失败: {}", e);
                    crate::error::DatawareError::DwdCalculateFailed(err_str)
                })
                .wrap_err("计算dwd test item失败")?,
        );

        // 计算dws test item
        log::info!("开始计算dws test item...");

        FtDwsTestItemService::new(self.config.dws_result_partition, params.test_area.clone(), self.config.clone())
            .calculate(
                &dwd_calculate_result.die_detail,
                &dwd_calculate_result.test_item_detail,
                &wafer_key,
                &params.test_area,
            )
            .await
            .map_err(|e| {
                let err_str = e.to_string();
                log::error!("计算dws test item失败: {}", e);
                crate::error::DatawareError::DwsCalculateFailed(err_str)
            })
            .wrap_err("计算dws test item失败")?;

        log::info!("dws test item计算完成");

        // 计算ADS YMS test item
        log::info!("开始计算ads yms test item...");
        FtYmsAdsTestItemService::new(self.config.clone())
            .calculate(
                &dwd_calculate_result.file_detail_map,
                &dwd_calculate_result.test_item_detail,
                &params.customer,
                &params.factory,
                &params.test_area,
                &params.device_id,
                &params.lot_type,
                &params.test_stage,
                &params.lot_id,
                &params.wafer_no,
                &params.data_version,
                &params.file_category,
            )
            .await
            .map_err(|e| {
                let err_str = e.to_string();
                log::error!("计算ads yms test item失败: {}", e);
                crate::error::DatawareError::AdsCalculateFailed(err_str)
            })
            .wrap_err("计算ads yms test item失败")?;

        log::info!("ads yms test item计算完成");

        drop(bump);
        let duration = start_time.elapsed();
        log::info!("ft test item task end...任务耗时：[{:?}]", duration);
        Ok(())
    }

    /// Execute tombstone operations for ClickHouse cleanup
    ///
    /// Corresponds to: FtTestItemTask.scala tombstone operations
    async fn execute_tombstone_operations(
        &self,
        customer: &str,
        factory: &str,
        test_area: &str,
        lot_id: &str,
        lot_type: &str,
        test_stage: &str,
        device_id: &str,
        wafer_no: &str,
    ) -> Result<()> {
        // 1. Create TestItemDetailHandler
        let handler = TestItemDetailHandler::new(self.config.dwd_db_name.clone(), self.config.insert_cluster_table);

        // 2. Calculate lot bucket
        let lot_bucket = DwdCommonUtil::get_lot_bucket(lot_id, self.config.lot_bucket_num);

        // 3. Build table full name
        let table_full_name = format!("{}.{}", handler.db_name(), handler.table_name());

        // 4. Call CkOperate.tombstoneCk
        CkOperate::tombstone_ck(
            customer,
            factory,
            test_area,
            lot_id,
            lot_type,
            test_stage,
            device_id,
            Some(lot_bucket),
            wafer_no,
            &table_full_name,
            &self.config.pick_random_ck_node_host(),
            &self.config.get_ck_address_list(),
            &self.config.ck_username,
            &self.config.ck_password,
            handler.partition_expr(),
            Some(chrono::Utc::now()),
        )
        .await
        .map_err(|e| {
            let err_str = e.to_string();
            log::error!("执行tombstone操作失败: {}", e);
            crate::error::DatawareError::TombstoneFailed(err_str)
        })?;

        Ok(())
    }

    /// Load die detail data from HDFS
    async fn load_die_detail_data(&self, die_detail_path: &str) -> Result<Vec<DieDetailParquet>> {
        log::info!("开始加载die detail数据: {}", die_detail_path);

        let die_detail = read_parquet::<DieDetailParquet>(die_detail_path, Some(&HdfsConfig::default()))
            .await
            .wrap_err("加载die detail数据失败")?;

        log::info!("成功加载die detail数据，共{}条记录", die_detail.len());
        Ok(die_detail)
    }

    /// Load test item data from HDFS
    async fn load_test_item_data(&self, test_item_data_path: &str) -> Result<Vec<Vec<TestItemDataParquet>>> {
        log::info!("开始加载test item数据: {}", test_item_data_path);

        let test_item_data = read_parquet_multi::<TestItemDataParquet>(test_item_data_path, Some(&HdfsConfig::default()))
            .await
            .wrap_err("加载test item数据失败")?;

        let total_records: usize = test_item_data.iter().map(|batch| batch.len()).sum();
        log::info!("成功加载test item数据，共{}批次，{}条记录", test_item_data.len(), total_records);
        Ok(test_item_data)
    }

    /// Create wafer key from task parameters
    fn create_wafer_key(&self, params: &FtTaskParams) -> WaferKey {
        WaferKey {
            customer: params.customer.clone(),
            sub_customer: params.sub_customer.clone(),
            factory: params.factory.clone(),
            factory_site: params.factory_site.clone(),
            test_area: params.test_area.clone(),
            test_stage: params.test_stage.clone(),
            device_id: params.device_id.clone(),
            lot_type: params.lot_type.clone(),
            lot_id: params.lot_id.clone(),
            wafer_no: params.wafer_no.clone(),
        }
    }
}


