use std::collections::HashMap;
use chrono::{DateTime, Utc};
use common::utils::date;
use serde_json;
use log::info;

use common::dto::dws::bin_failitem::BinFailitem;
use common::dto::dws::sub_bin_failitem::SubBinFailitem;
use common::dto::dws::bin_key::BinKey;
use common::dto::dws::bin_relation::BinRelation;
use common::dto::dws::test_program_test_item_bin_relation::TestProgramTestItemBinRelation;
use common::dws::model::mysql::dw_test_program_test_plan::DwTestProgramTestPlan;
use common::model::constant::{COMMA, EMPTY, SYSTEM};
use common::model::key::LotKey;
use common::repository::mysql::test_program_test_plan_repository::TestProgramTestPlanRepository;
use mysql_provider::MySqlConfig;

#[derive(Debug, Clone)]
pub struct BinFailitemCommonService;

impl BinFailitemCommonService {
    pub fn new() -> Self {
        Self
    }

    /// Calculate BinFailitem from SubBinFailitem
    /// Equivalent to calculateBinFailitem in Scala
    pub fn calculate_bin_failitem(&self, item: &SubBinFailitem) -> BinFailitem {
        let now = chrono::Utc::now();
        let create_hour_key = date::get_day_hour(now);
        let create_day_key = date::get_day(now);
        
        let first_fail_item = item.FIRST_FAIL_ITEM.as_ref();
        
        BinFailitem {
            CUSTOMER: item.CUSTOMER.clone(),
            SUB_CUSTOMER: item.SUB_CUSTOMER.clone(),
            UPLOAD_TYPE: item.UPLOAD_TYPE.clone(),
            FACTORY: item.FACTORY.clone(),
            FACTORY_SITE: item.FACTORY_SITE.clone(),
            FAB: item.FAB.clone(),
            FAB_SITE: item.FAB_SITE.clone(),
            TEST_AREA: item.TEST_AREA.clone(),
            TEST_STAGE: item.TEST_STAGE.clone(),
            DEVICE_ID: item.DEVICE_ID.clone(),
            LOT_TYPE: item.LOT_TYPE.clone(),
            TEST_PROGRAM: item.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: item.TEST_PROGRAM_VERSION.clone(),
            TEST_NUM: first_fail_item.and_then(|f| f.TEST_NUM),
            TEST_TXT: first_fail_item.and_then(|f| f.TEST_TXT.clone()),
            TEST_ITEM: first_fail_item.and_then(|f| f.TEST_ITEM.clone()),
            TESTITEM_TYPE: first_fail_item.and_then(|f| f.TESTITEM_TYPE.clone()),
            UNITS: first_fail_item.and_then(|f| f.UNITS.clone()),
            ORIGIN_UNITS: first_fail_item.and_then(|f| f.ORIGIN_UNITS.clone()),
            HBIN_NUM: item.HBIN_NUM,
            HBIN_NUM_KEY: item.HBIN_NUM.map(|n| n.to_string()),
            HBIN_NAM: item.HBIN_NAM.clone(),
            HBIN_PF: item.HBIN_PF.clone(),
            HBIN: item.HBIN.clone(),
            SBIN_NUM: item.SBIN_NUM,
            SBIN_NUM_KEY: item.SBIN_NUM.map(|n| n.to_string()),
            SBIN_NAM: item.SBIN_NAM.clone(),
            SBIN_PF: item.SBIN_PF.clone(),
            SBIN: item.SBIN.clone(),
            CREATE_HOUR_KEY: Some(create_hour_key),
            CREATE_DAY_KEY: Some(create_day_key),
            CREATE_TIME: Some(now.timestamp_millis()),
            CREATE_USER: Some(SYSTEM.to_string()),
            VERSION: Some(now.timestamp_millis()),
            UPLOAD_TIME: item.UPLOAD_TIME,
        }
    }

    /// Calculate bin relation from BinFailitem records
    /// Equivalent to calBinRelation in Scala
    pub fn cal_bin_relation(&self, records: &[BinFailitem]) -> Option<TestProgramTestItemBinRelation> {
        if records.is_empty() {
            return None;
        }
        
        let head = &records[0];
        let bin_relations: Vec<BinRelation> = records.iter().map(|t| {
            BinRelation {
                hbin_num: t.HBIN_NUM.map(|n| n as i64),
                hbin_nam: t.HBIN_NAM.clone(),
                hbin_pf: t.HBIN_PF.clone(),
                hbin: t.HBIN.clone(),
                sbin_num: t.SBIN_NUM.map(|n| n as i64),
                sbin_nam: t.SBIN_NAM.clone(),
                sbin_pf: t.SBIN_PF.clone(),
                sbin: t.SBIN.clone(),
            }
        }).collect();
        
        let bin_relation_map = self.convert_to_bin_relation_map(&bin_relations);
        
        Some(TestProgramTestItemBinRelation {
            fab: head.FAB.clone(),
            fab_site: head.FAB_SITE.clone(),
            test_program: head.TEST_PROGRAM.clone(),
            test_num: head.TEST_NUM.map(|n| n as i64),
            test_txt: head.TEST_TXT.clone(),
            test_item: head.TEST_ITEM.clone(),
            testitem_type: head.TESTITEM_TYPE.clone(),
            bin_relation: Some(bin_relation_map),
        })
    }

    /// Convert BinRelation array to HashMap with BinKey
    /// Equivalent to convertToBinRelationMap in Scala
    pub fn convert_to_bin_relation_map(&self, bin_relations: &[BinRelation]) -> HashMap<BinKey, BinRelation> {
        bin_relations.iter().map(|t| {
            let key = BinKey {
                sbin_num: t.sbin_num,
                sbin_nam: t.sbin_nam.clone(),
                hbin_num: t.hbin_num,
                hbin_nam: t.hbin_nam.clone(),
            };
            (key, t.clone())
        }).collect()
    }

    /// Calculate test program test plan
    /// Equivalent to calTestProgramTestPlan in Scala
    pub async fn cal_test_program_test_plan(
        &self,
        bin_relations: &[TestProgramTestItemBinRelation],
        redis_provider: &redis_provider::provider::RedisProvider,
        mysql_config: MySqlConfig,
        lot_key: &LotKey,
        upload_type: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Group bin relations by test program
        let mut program_with_test_item_map: HashMap<String, HashMap<String, &TestProgramTestItemBinRelation>> = HashMap::new();
        
        for relation in bin_relations {
            if let Some(test_program) = &relation.test_program {
                let program_map = program_with_test_item_map.entry(test_program.clone()).or_insert_with(HashMap::new);
                if let Some(test_item) = &relation.test_item {
                    program_map.insert(test_item.clone(), relation);
                }
            }
        }

        // Process each test program
        for (program, test_item_map) in program_with_test_item_map {
            let test_program_test_plan_repository = TestProgramTestPlanRepository::new(mysql_config.clone()).await?;
            
            // Check if we can update test program test plan bin relation
            let can_update_programs = test_program_test_plan_repository
                .can_update_test_program_test_plan_bin_relation(
                    lot_key,
                    upload_type,
                    &[program.clone()],
                )
                .await?;

            if can_update_programs.contains(&program) {
                let mut test_program_test_plan_lock = redis_provider.get_test_program_test_plan_lock(
                    lot_key,
                    upload_type,
                    &program,
                );
                
                // Acquire lock
                test_program_test_plan_lock.try_lock_with_timeout(std::time::Duration::from_secs(300))?;
                
                // Use a closure to ensure unlock is called even if an error occurs
                let result = async {
                    // Query existing program test plans
                    let exists_program_test_plan_map = test_program_test_plan_repository
                        .query_program_plan(lot_key, upload_type, &program)
                        .await?;

                    // Check if any existing plan has manual import flag set
                    let has_manual_import = exists_program_test_plan_map
                        .values()
                        .any(|plan| plan.bin_relation_manual_import_flag.unwrap_or(0) == 1);

                    if has_manual_import {
                        info!("{} 存在导入bin_relation数据，跳过写入", test_program_test_plan_lock.key());
                    } else {
                        info!("{} 可以写入bin_relation数据", test_program_test_plan_lock.key());
                        self.write_test_program_test_plan(
                            &test_item_map,
                            &exists_program_test_plan_map,
                            &test_program_test_plan_repository,
                            lot_key,
                            upload_type,
                        ).await?;
                    }
                    
                    Ok::<(), Box<dyn std::error::Error>>(())
                }.await;
                
                // Always unlock
                test_program_test_plan_lock.unlock()?;
                
                // Propagate any error that occurred
                result?;
            }
        }

        Ok(())
    }

    /// Write test program test plan data
    /// Equivalent to writeTestProgramTestPlan in Scala
    async fn write_test_program_test_plan(
        &self,
        item_with_bin_relation_map: &HashMap<String, &TestProgramTestItemBinRelation>,
        exists_program_test_plan_map: &HashMap<String, DwTestProgramTestPlan>,
        test_program_test_plan_repository: &TestProgramTestPlanRepository,
        lot_key: &LotKey,
        upload_type: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let _now = chrono::Utc::now().timestamp_millis();
        let now_datetime = chrono::Utc::now().naive_utc();
        
        // Prepare update plans
        let mut update_plans = Vec::new();
        for (test_item, existing_plan) in exists_program_test_plan_map {
            if let Some(new_relation) = item_with_bin_relation_map.get(test_item) {
                let mut update_plan = existing_plan.clone();
                
                // Update fab and fab_site if not blank
                if let Some(fab) = &new_relation.fab {
                    if !fab.trim().is_empty() {
                        update_plan.fab = Some(fab.clone());
                    }
                }
                if let Some(fab_site) = &new_relation.fab_site {
                    if !fab_site.trim().is_empty() {
                        update_plan.fab_site = Some(fab_site.clone());
                    }
                }
                
                // Merge bin relations
                let existing_bin_relations = self.parse_exists_bin_relation(
                    update_plan.bin_relation.as_deref().unwrap_or(""),
                )?;
                let empty_map = HashMap::new();
                let new_bin_relations = new_relation.bin_relation.as_ref().unwrap_or(&empty_map);
                
                let mut merged_relations = existing_bin_relations;
                merged_relations.extend(new_bin_relations.clone());
                let merged_relations_vec: Vec<BinRelation> = merged_relations.into_values().collect();
                
                update_plan.bin_relation = Some(serde_json::to_string(&merged_relations_vec)?);
                update_plan.hbins = Some(self.sort_hbin_and_merge(&merged_relations_vec));
                update_plan.sbins = Some(self.sort_sbin_and_merge(&merged_relations_vec));
                update_plan.update_user = Some(SYSTEM.to_string());
                update_plan.update_time = Some(now_datetime);
                
                update_plans.push(update_plan);
            }
        }
        
        // Prepare insert plans
        let mut insert_plans = Vec::new();
        for (test_item, relation) in item_with_bin_relation_map {
            if !exists_program_test_plan_map.contains_key(test_item) {
                let new_bin_relations: Vec<BinRelation> = relation.bin_relation
                    .as_ref()
                    .map(|map| map.values().cloned().collect())
                    .unwrap_or_default();
                
                let new_plan = DwTestProgramTestPlan {
                    id: None,
                    customer: Some(lot_key.customer.clone()),
                    sub_customer: Some(lot_key.sub_customer.clone()),
                    upload_type: Some(upload_type.to_string()),
                    test_area: Some(lot_key.test_area.clone()),
                    factory: Some(lot_key.factory.clone()),
                    factory_site: Some(lot_key.factory_site.clone()),
                    fab: relation.fab.clone(),
                    fab_site: relation.fab_site.clone(),
                    device_id: Some(lot_key.device_id.clone()),
                    test_stage: Some(lot_key.test_stage.clone()),
                    lot_type: Some(lot_key.lot_type.clone()),
                    test_program: relation.test_program.clone(),
                    test_item: relation.test_item.clone(),
                    test_order: None,
                    testitem_type: relation.testitem_type.clone(),
                    test_num: relation.test_num,
                    test_txt: relation.test_txt.clone(),
                    bin_relation: Some(serde_json::to_string(&new_bin_relations)?),
                    hbins: Some(self.sort_hbin_and_merge(&new_bin_relations)),
                    sbins: Some(self.sort_sbin_and_merge(&new_bin_relations)),
                    unit_scale: None,
                    custom_unit: Some(EMPTY.to_string()),
                    test_order_manual_import_flag: Some(0),
                    bin_relation_manual_import_flag: Some(0),
                    create_time: Some(now_datetime),
                    update_time: Some(now_datetime),
                    create_user: Some(SYSTEM.to_string()),
                    update_user: Some(SYSTEM.to_string()),
                };
                
                insert_plans.push(new_plan);
            }
        }
        
        // Save or update plans
        let mut all_plans = insert_plans;
        all_plans.extend(update_plans);
        
        if !all_plans.is_empty() {
            test_program_test_plan_repository.save_or_update_test_program_test_plan(all_plans).await?;
        }
        
        Ok(())
    }

    /// Sort SBIN and merge
    /// Equivalent to sortSbinAndMerge in Scala
    fn sort_sbin_and_merge(&self, bin_relations: &[BinRelation]) -> String {
        let mut with_sbin_num: Vec<_> = bin_relations
            .iter()
            .filter(|r| r.sbin_num.is_some())
            .collect();
        with_sbin_num.sort_by_key(|r| r.sbin_num);
        
        let mut without_sbin_num: Vec<_> = bin_relations
            .iter()
            .filter(|r| r.sbin_num.is_none())
            .collect();
        without_sbin_num.sort_by(|a, b| {
            a.sbin.as_deref().unwrap_or("").cmp(b.sbin.as_deref().unwrap_or(""))
        });
        
        let mut result: Vec<String> = with_sbin_num
            .into_iter()
            .filter_map(|r| r.sbin.clone())
            .collect();
        result.extend(
            without_sbin_num
                .into_iter()
                .filter_map(|r| r.sbin.clone())
        );
        
        // Remove duplicates while preserving order
        let mut unique_result = Vec::new();
        let mut seen = std::collections::HashSet::new();
        for item in result {
            if seen.insert(item.clone()) {
                unique_result.push(item);
            }
        }
        
        unique_result.join(COMMA)
    }

    /// Sort HBIN and merge
    /// Equivalent to sortHbinAndMerge in Scala
    fn sort_hbin_and_merge(&self, bin_relations: &[BinRelation]) -> String {
        let mut with_hbin_num: Vec<_> = bin_relations
            .iter()
            .filter(|r| r.hbin_num.is_some())
            .collect();
        with_hbin_num.sort_by_key(|r| r.hbin_num);
        
        let mut without_hbin_num: Vec<_> = bin_relations
            .iter()
            .filter(|r| r.hbin_num.is_none())
            .collect();
        without_hbin_num.sort_by(|a, b| {
            a.hbin.as_deref().unwrap_or("").cmp(b.hbin.as_deref().unwrap_or(""))
        });
        
        let mut result: Vec<String> = with_hbin_num
            .into_iter()
            .filter_map(|r| r.hbin.clone())
            .collect();
        result.extend(
            without_hbin_num
                .into_iter()
                .filter_map(|r| r.hbin.clone())
        );
        
        // Remove duplicates while preserving order
        let mut unique_result = Vec::new();
        let mut seen = std::collections::HashSet::new();
        for item in result {
            if seen.insert(item.clone()) {
                unique_result.push(item);
            }
        }
        
        unique_result.join(COMMA)
    }

    /// Parse existing bin relation from JSON string
    /// Equivalent to parseExistsBinRelation in Scala
    fn parse_exists_bin_relation(&self, bin_relation: &str) -> Result<HashMap<BinKey, BinRelation>, Box<dyn std::error::Error>> {
        if bin_relation.trim().is_empty() {
            Ok(HashMap::new())
        } else {
            let bin_relations: Vec<BinRelation> = serde_json::from_str(bin_relation)?;
            Ok(self.convert_to_bin_relation_map(&bin_relations))
        }
    }
}